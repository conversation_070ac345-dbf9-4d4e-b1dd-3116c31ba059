<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701;NU1604;NU1602</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.IO.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\core\NuGone.Application\NuGone.Application.csproj" />
  </ItemGroup>
</Project>
