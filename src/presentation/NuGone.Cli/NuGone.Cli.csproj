<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701;NU1604;NU1602</WarningsNotAsErrors>
    <AssemblyName>nugone</AssemblyName>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>nugone</ToolCommandName>
    <PackageId>NuGone</PackageId>
    <PackageDescription>Automatically detect and remove unused NuGet package references in your .NET projects.</PackageDescription>
    <PackageTags>nuget;packages;cleanup;dotnet;cli</PackageTags>
    <RepositoryUrl>https://github.com/ahmet-cetinkaya/nugone</RepositoryUrl>
    <PackageLicenseExpression>GPL-3.0-or-later</PackageLicenseExpression>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Spectre.Console.Cli" Version="0.50.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\core\NuGone.Application\NuGone.Application.csproj" />
    <ProjectReference Include="..\..\infrastructure\NuGone.FileSystem\NuGone.FileSystem.csproj" />
    <ProjectReference Include="..\..\infrastructure\NuGone.NuGet\NuGone.NuGet.csproj" />
  </ItemGroup>
</Project>
