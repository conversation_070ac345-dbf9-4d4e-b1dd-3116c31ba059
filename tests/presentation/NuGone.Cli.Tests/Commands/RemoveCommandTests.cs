using FluentAssertions;
using NuGone.Cli.Features.RemoveCommand.Commands;
using NuGone.Cli.Shared.Constants;
using NuGone.Cli.Shared.Models;
using Spectre.Console.Cli;
using System.IO.Abstractions.TestingHelpers;

namespace NuGone.Cli.Tests.Commands;

/// <summary>
/// Tests for RemoveCommand class.
/// Validates RFC-0001: CLI Architecture And Command Design - RemoveCommand implementation.
/// </summary>
public class RemoveCommandTests
{
    private readonly MockFileSystem _fileSystem;
    private readonly string _testProjectPath;
    private readonly string _testSolutionPath;

    public RemoveCommandTests()
    {
        _fileSystem = new MockFileSystem();
        _testProjectPath = Path.Combine("test", "project", "Test.csproj");
        _testSolutionPath = Path.Combine("test", "solution", "Test.sln");

        // Setup test files
        _fileSystem.AddFile(_testProjectPath, new MockFileData("<Project></Project>"));
        _fileSystem.AddFile(_testSolutionPath, new MockFileData("Microsoft Visual Studio Solution File"));
    }

    #region Settings Tests

    [Fact]
    public void Settings_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings();

        // Assert
        settings.ProjectPath.Should().BeNull();
        settings.ExcludePackages.Should().BeNull();
        settings.DryRun.Should().BeFalse();
        settings.SkipConfirmation.Should().BeFalse();
        settings.Format.Should().Be("text");
        settings.OutputFile.Should().BeNull();
        settings.Verbose.Should().BeFalse();
    }

    [Fact]
    public void Settings_ShouldAcceptProjectPath()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings
        {
            ProjectPath = "/path/to/project"
        };

        // Assert
        settings.ProjectPath.Should().Be("/path/to/project");
    }

    [Fact]
    public void Settings_ShouldAcceptExcludePackages()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings
        {
            ExcludePackages = new[] { "Package1", "Package2" }
        };

        // Assert
        settings.ExcludePackages.Should().NotBeNull();
        settings.ExcludePackages.Should().HaveCount(2);
        settings.ExcludePackages.Should().Contain("Package1");
        settings.ExcludePackages.Should().Contain("Package2");
    }

    [Fact]
    public void Settings_ShouldAcceptDryRunFlag()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings { DryRun = true };

        // Assert
        settings.DryRun.Should().BeTrue();
    }

    [Fact]
    public void Settings_ShouldAcceptSkipConfirmationFlag()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings { SkipConfirmation = true };

        // Assert
        settings.SkipConfirmation.Should().BeTrue();
    }

    [Fact]
    public void Settings_ShouldAcceptFormatOptions()
    {
        // Arrange & Act
        var jsonSettings = new RemoveCommand.Settings { Format = "json" };
        var textSettings = new RemoveCommand.Settings { Format = "text" };

        // Assert
        jsonSettings.Format.Should().Be("json");
        textSettings.Format.Should().Be("text");
    }

    [Fact]
    public void Settings_ShouldAcceptVerboseFlag()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings { Verbose = true };

        // Assert
        settings.Verbose.Should().BeTrue();
    }

    [Fact]
    public void Settings_ShouldAcceptOutputFile()
    {
        // Arrange & Act
        var settings = new RemoveCommand.Settings { OutputFile = "output.json" };

        // Assert
        settings.OutputFile.Should().Be("output.json");
    }

    #endregion

    #region Command Execution Tests

    [Fact]
    public void RemoveCommand_ShouldInheritFromBaseCommand()
    {
        // Arrange & Act
        var command = new RemoveCommand();

        // Assert
        command.Should().BeAssignableTo<BaseCommand<RemoveCommand.Settings>>();
    }

    [Fact]
    public void RemoveCommand_ShouldNotImplementIAsyncCommand()
    {
        // Arrange & Act
        var command = new RemoveCommand();

        // Assert
        command.Should().NotBeAssignableTo<IAsyncCommand<RemoveCommand.Settings>>();
    }

    [Fact]
    public void RemoveCommand_ShouldValidateProjectPath()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            ProjectPath = "/non/existent/path"
        };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_ARGUMENT");
        result.Error.Message.Should().Contain("Project path does not exist");
    }

    [Fact]
    public void RemoveCommand_ShouldUseCurrentDirectoryWhenProjectPathIsNull()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings { ProjectPath = null };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(Path.GetFullPath(Directory.GetCurrentDirectory()));
    }

    #endregion

    #region Validation Tests

    [Fact]
    public void RemoveCommand_ShouldValidateSettingsSuccessfully()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            ProjectPath = Directory.GetCurrentDirectory(),
            DryRun = false,
            SkipConfirmation = true
        };

        // Act
        var result = command.TestValidateRemoveSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void RemoveCommand_ShouldFailValidationForCriticalPackageExclusion()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            ExcludePackages = new[] { "critical-package" }
        };

        // Act
        var result = command.TestPerformRemoval(Directory.GetCurrentDirectory(), settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("OPERATION_FAILED");
        result.Error.Message.Should().Contain("Cannot exclude critical system packages");
    }

    [Fact]
    public void RemoveCommand_ShouldFailForReadOnlyPath()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings();
        var readOnlyPath = Path.Combine("readonly", "project");

        // Act
        var result = command.TestPerformRemoval(readOnlyPath, settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("ACCESS_DENIED");
    }

    [Fact]
    public void RemoveCommand_ShouldSucceedForValidSettings()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            ExcludePackages = new[] { "valid-package" }
        };

        // Act
        var result = command.TestPerformRemoval(Directory.GetCurrentDirectory(), settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    #endregion

    #region Confirmation Logic Tests

    [Fact]
    public void RemoveCommand_ShouldSkipConfirmationWhenDryRun()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings { DryRun = true };

        // Act
        var needsConfirmation = command.TestNeedsConfirmation(settings);

        // Assert
        needsConfirmation.Should().BeFalse();
    }

    [Fact]
    public void RemoveCommand_ShouldSkipConfirmationWhenSkipConfirmationIsTrue()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings { SkipConfirmation = true };

        // Act
        var needsConfirmation = command.TestNeedsConfirmation(settings);

        // Assert
        needsConfirmation.Should().BeFalse();
    }

    [Fact]
    public void RemoveCommand_ShouldRequireConfirmationByDefault()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            DryRun = false,
            SkipConfirmation = false
        };

        // Act
        var needsConfirmation = command.TestNeedsConfirmation(settings);

        // Assert
        needsConfirmation.Should().BeTrue();
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public void RemoveCommand_ShouldReturnErrorForInvalidProjectPath()
    {
        // Arrange
        var command = new TestableRemoveCommand();
        var settings = new RemoveCommand.Settings
        {
            ProjectPath = "/invalid/path/that/does/not/exist"
        };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_ARGUMENT");
        result.Error.ExitCode.Should().Be(ExitCodes.InvalidArgument);
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Testable version of RemoveCommand that exposes protected methods for testing.
    /// </summary>
    private class TestableRemoveCommand : RemoveCommand
    {
        public Result<string> TestValidateAndResolveProjectPath(string? projectPath)
        {
            return ValidateAndResolveProjectPath(projectPath);
        }

        public Result TestValidateRemoveSettings(Settings settings)
        {
            return ValidateRemoveSettings(settings);
        }

        public Result TestPerformRemoval(string projectPath, Settings settings)
        {
            return PerformRemoval(projectPath, settings);
        }

        public bool TestNeedsConfirmation(Settings settings)
        {
            return !settings.DryRun && !settings.SkipConfirmation;
        }

        public bool TestIsVerboseMode(Settings? settings)
        {
            return IsVerboseMode(settings);
        }

        // Override to prevent actual execution during tests
        protected override Result<int> ExecuteCommand(CommandContext context, Settings settings)
        {
            return ExitCodes.Success;
        }
    }

    #endregion
}
