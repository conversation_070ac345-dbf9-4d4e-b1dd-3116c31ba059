using System.IO.Abstractions.TestingHelpers;
using FluentAssertions;
using NuGone.Cli.Features.AnalyzeCommand.Commands;
using NuGone.Cli.Shared.Constants;
using NuGone.Cli.Shared.Models;
using Spectre.Console.Cli;
using Spectre.Console.Testing;
using Xunit;

namespace NuGone.Cli.Tests.Commands;

/// <summary>
/// Tests for AnalyzeCommand class.
/// Validates RFC-0001: CLI Architecture And Command Design - AnalyzeCommand implementation.
/// </summary>
public class AnalyzeCommandTests
{
    private readonly MockFileSystem _fileSystem;
    private readonly string _testProjectPath;
    private readonly string _testSolutionPath;

    public AnalyzeCommandTests()
    {
        _fileSystem = new MockFileSystem();
        _testProjectPath = Path.Combine("test", "project", "Test.csproj");
        _testSolutionPath = Path.Combine("test", "solution", "Test.sln");

        // Setup test files
        _fileSystem.AddFile(_testProjectPath, new MockFileData("<Project></Project>"));
        _fileSystem.AddFile(
            _testSolutionPath,
            new MockFileData("Microsoft Visual Studio Solution File")
        );
    }

    #region Settings Tests

    [Fact]
    public void Settings_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings();

        // Assert
        settings.ProjectPath.Should().BeNull();
        settings.DryRun.Should().BeTrue(); // Analyze is always dry-run by nature
        settings.Format.Should().Be("text");
        settings.OutputFile.Should().BeNull();
        settings.ExcludePackages.Should().BeNull();
        settings.Verbose.Should().BeFalse();
    }

    [Fact]
    public void Settings_ShouldAcceptProjectPath()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { ProjectPath = "/path/to/project" };

        // Assert
        settings.ProjectPath.Should().Be("/path/to/project");
    }

    [Fact]
    public void Settings_ShouldAcceptFormatOptions()
    {
        // Arrange & Act
        var jsonSettings = new AnalyzeCommand.Settings { Format = "json" };
        var textSettings = new AnalyzeCommand.Settings { Format = "text" };

        // Assert
        jsonSettings.Format.Should().Be("json");
        textSettings.Format.Should().Be("text");
    }

    [Fact]
    public void Settings_ShouldAcceptExcludePackages()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings
        {
            ExcludePackages = new[] { "Package1", "Package2" },
        };

        // Assert
        settings.ExcludePackages.Should().NotBeNull();
        settings.ExcludePackages.Should().HaveCount(2);
        settings.ExcludePackages.Should().Contain("Package1");
        settings.ExcludePackages.Should().Contain("Package2");
    }

    [Fact]
    public void Settings_ShouldAcceptVerboseFlag()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { Verbose = true };

        // Assert
        settings.Verbose.Should().BeTrue();
    }

    [Fact]
    public void Settings_ShouldAcceptOutputFile()
    {
        // Arrange & Act
        var settings = new AnalyzeCommand.Settings { OutputFile = "output.json" };

        // Assert
        settings.OutputFile.Should().Be("output.json");
    }

    #endregion

    #region Command Execution Tests

    [Fact]
    public void AnalyzeCommand_ShouldInheritFromBaseCommand()
    {
        // Arrange & Act
        var command = new AnalyzeCommand();

        // Assert
        command.Should().BeAssignableTo<BaseCommand<AnalyzeCommand.Settings>>();
    }

    [Fact]
    public void AnalyzeCommand_ShouldImplementIAsyncCommand()
    {
        // Arrange & Act
        var command = new AnalyzeCommand();

        // Assert
        command.Should().BeAssignableTo<IAsyncCommand<AnalyzeCommand.Settings>>();
    }

    [Fact]
    public void AnalyzeCommand_ShouldValidateProjectPath()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { ProjectPath = "/non/existent/path" };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_ARGUMENT");
        result.Error.Message.Should().Contain("Project path does not exist");
    }

    [Fact]
    public void AnalyzeCommand_ShouldUseCurrentDirectoryWhenProjectPathIsNull()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { ProjectPath = null };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(Path.GetFullPath(Directory.GetCurrentDirectory()));
    }

    [Theory]
    [InlineData("json")]
    [InlineData("JSON")]
    [InlineData("Json")]
    public void AnalyzeCommand_ShouldAcceptJsonFormat(string format)
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = format };

        // Act
        var isJsonFormat = command.TestIsJsonFormat(settings);

        // Assert
        isJsonFormat.Should().BeTrue();
    }

    [Theory]
    [InlineData("text")]
    [InlineData("TEXT")]
    [InlineData("Text")]
    [InlineData("")]
    [InlineData(null)]
    public void AnalyzeCommand_ShouldTreatNonJsonAsTextFormat(string? format)
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = format };

        // Act
        var isJsonFormat = command.TestIsJsonFormat(settings);

        // Assert
        isJsonFormat.Should().BeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldDetectVerboseMode()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var verboseSettings = new AnalyzeCommand.Settings { Verbose = true };
        var nonVerboseSettings = new AnalyzeCommand.Settings { Verbose = false };

        // Act
        var isVerbose = command.TestIsVerboseMode(verboseSettings);
        var isNotVerbose = command.TestIsVerboseMode(nonVerboseSettings);

        // Assert
        isVerbose.Should().BeTrue();
        isNotVerbose.Should().BeFalse();
    }

    #endregion

    #region Output Format Tests

    [Fact]
    public void AnalyzeCommand_ShouldShowSuccessMessageForTextFormat()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "text", Verbose = false };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.Should().BeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldNotShowSuccessMessageForJsonFormatWithoutVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = false };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.Should().BeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowSuccessMessageForJsonFormatWithVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = true };

        // Act
        var shouldShowMessage = command.TestShouldShowSuccessMessage(settings);

        // Assert
        shouldShowMessage.Should().BeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowProgressMessageForTextFormat()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "text", Verbose = false };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.Should().BeTrue();
    }

    [Fact]
    public void AnalyzeCommand_ShouldNotShowProgressMessageForJsonFormatWithoutVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = false };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.Should().BeFalse();
    }

    [Fact]
    public void AnalyzeCommand_ShouldShowProgressMessageForJsonFormatWithVerbose()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings { Format = "json", Verbose = true };

        // Act
        var shouldShowProgress = command.TestShouldShowProgressMessage(settings);

        // Assert
        shouldShowProgress.Should().BeTrue();
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public void AnalyzeCommand_ShouldReturnErrorForInvalidProjectPath()
    {
        // Arrange
        var command = new TestableAnalyzeCommand();
        var settings = new AnalyzeCommand.Settings
        {
            ProjectPath = "/invalid/path/that/does/not/exist",
        };

        // Act
        var result = command.TestValidateAndResolveProjectPath(settings.ProjectPath);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_ARGUMENT");
        result.Error.ExitCode.Should().Be(ExitCodes.InvalidArgument);
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Testable version of AnalyzeCommand that exposes protected methods for testing.
    /// </summary>
    private class TestableAnalyzeCommand : AnalyzeCommand
    {
        public Result<string> TestValidateAndResolveProjectPath(string? projectPath)
        {
            return ValidateAndResolveProjectPath(projectPath);
        }

        public bool TestIsVerboseMode(Settings settings)
        {
            return IsVerboseMode(settings);
        }

        public bool TestIsJsonFormat(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() == "json";
        }

        public bool TestShouldShowSuccessMessage(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() != "json" || settings.Verbose;
        }

        public bool TestShouldShowProgressMessage(Settings settings)
        {
            return settings.Format?.ToLowerInvariant() != "json" || settings.Verbose;
        }

        // Override to prevent actual execution during tests
        protected override async Task<Result<int>> ExecuteCommandAsync(
            CommandContext context,
            Settings settings
        )
        {
            await Task.CompletedTask;
            return ExitCodes.Success;
        }
    }

    #endregion
}
