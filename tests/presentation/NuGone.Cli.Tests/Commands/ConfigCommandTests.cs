using FluentAssertions;
using NuGone.Cli.Features.ConfigCommand.Commands;
using NuGone.Cli.Shared.Constants;
using NuGone.Cli.Shared.Models;
using NuGone.Cli.Shared.Utilities;
using Spectre.Console.Cli;
using Xunit;

namespace NuGone.Cli.Tests.Commands;

/// <summary>
/// Tests for ConfigCommand class.
/// Validates RFC-0001: CLI Architecture And Command Design - ConfigCommand implementation.
/// </summary>
public class ConfigCommandTests
{
    #region Settings Tests

    [Fact]
    public void Settings_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var settings = new ConfigCommand.Settings();

        // Assert
        settings.Action.Should().BeNull();
        settings.Key.Should().BeNull();
        settings.Value.Should().BeNull();
        settings.Global.Should().BeFalse();
    }

    [Fact]
    public void Settings_ShouldAcceptAction()
    {
        // Arrange & Act
        var settings = new ConfigCommand.Settings { Action = "list" };

        // Assert
        settings.Action.Should().Be("list");
    }

    [Fact]
    public void Settings_ShouldAcceptKey()
    {
        // Arrange & Act
        var settings = new ConfigCommand.Settings { Key = "excludePatterns" };

        // Assert
        settings.Key.Should().Be("excludePatterns");
    }

    [Fact]
    public void Settings_ShouldAcceptValue()
    {
        // Arrange & Act
        var settings = new ConfigCommand.Settings { Value = "System.Text.Json" };

        // Assert
        settings.Value.Should().Be("System.Text.Json");
    }

    [Fact]
    public void Settings_ShouldAcceptGlobalFlag()
    {
        // Arrange & Act
        var settings = new ConfigCommand.Settings { Global = true };

        // Assert
        settings.Global.Should().BeTrue();
    }

    #endregion

    #region Command Execution Tests

    [Fact]
    public void ConfigCommand_ShouldInheritFromBaseCommand()
    {
        // Arrange & Act
        var command = new ConfigCommand();

        // Assert
        command.Should().BeAssignableTo<BaseCommand<ConfigCommand.Settings>>();
    }

    [Fact]
    public void ConfigCommand_ShouldNotImplementIAsyncCommand()
    {
        // Arrange & Act
        var command = new ConfigCommand();

        // Assert
        command.Should().NotBeAssignableTo<IAsyncCommand<ConfigCommand.Settings>>();
    }

    [Fact]
    public void ConfigCommand_ShouldReturnSuccessForValidSettings()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var context = new CommandContext(
            Array.Empty<string>(),
            new FakeRemainingArguments(),
            "config",
            null
        );
        var settings = new ConfigCommand.Settings { Action = "list" };

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.Success);
    }

    #endregion

    #region Validation Tests

    [Theory]
    [InlineData("get")]
    [InlineData("list")]
    [InlineData("reset")]
    [InlineData("GET")]
    [InlineData("LIST")]
    [InlineData("RESET")]
    public void ConfigCommand_ShouldAcceptValidActions(string action)
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings { Action = action };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Theory]
    [InlineData("set")]
    [InlineData("SET")]
    public void ConfigCommand_ShouldAcceptSetActionWithKeyAndValue(string action)
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = action,
            Key = "testKey",
            Value = "testValue",
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Theory]
    [InlineData("invalid")]
    [InlineData("delete")]
    [InlineData("create")]
    [InlineData("unknown")]
    public void ConfigCommand_ShouldRejectInvalidActions(string action)
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings { Action = action };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Action must be one of: get, set, list, reset");
        result.Error.Details.Should().ContainKey("ProvidedAction");
        result.Error.Details["ProvidedAction"].Should().Be(action);
    }

    [Fact]
    public void ConfigCommand_ShouldAcceptNullAction()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings { Action = null };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ConfigCommand_ShouldAcceptEmptyAction()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings { Action = string.Empty };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ConfigCommand_ShouldRequireKeyAndValueForSetAction()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = "testKey",
            Value = "testValue",
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    [Fact]
    public void ConfigCommand_ShouldFailWhenSetActionMissingKey()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = null,
            Value = "testValue",
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Both key and value are required for 'set' action");
    }

    [Fact]
    public void ConfigCommand_ShouldFailWhenSetActionMissingValue()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = "testKey",
            Value = null,
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Both key and value are required for 'set' action");
    }

    [Fact]
    public void ConfigCommand_ShouldFailWhenSetActionMissingBothKeyAndValue()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = null,
            Value = null,
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Both key and value are required for 'set' action");
    }

    [Fact]
    public void ConfigCommand_ShouldFailWhenSetActionHasEmptyKey()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = string.Empty,
            Value = "testValue",
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Both key and value are required for 'set' action");
    }

    [Fact]
    public void ConfigCommand_ShouldFailWhenSetActionHasEmptyValue()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = "set",
            Key = "testKey",
            Value = string.Empty,
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.Message.Should().Contain("Both key and value are required for 'set' action");
    }

    [Theory]
    [InlineData("get")]
    [InlineData("list")]
    [InlineData("reset")]
    public void ConfigCommand_ShouldNotRequireKeyAndValueForNonSetActions(string action)
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings
        {
            Action = action,
            Key = null,
            Value = null,
        };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    #endregion

    #region Error Handling Tests

    [Fact]
    public void ConfigCommand_ShouldReturnValidationErrorForInvalidAction()
    {
        // Arrange
        var command = new TestableConfigCommand();
        var settings = new ConfigCommand.Settings { Action = "invalid-action" };

        // Act
        var result = command.TestValidateConfigSettings(settings);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("VALIDATION_FAILED");
        result.Error.ExitCode.Should().Be(ExitCodes.InvalidArgument);
    }

    #endregion

    #region Helper Classes

    /// <summary>
    /// Testable version of ConfigCommand that exposes protected methods for testing.
    /// </summary>
    private class TestableConfigCommand : ConfigCommand
    {
        public Result TestValidateConfigSettings(Settings settings)
        {
            // Since ValidateConfigSettings is private, we'll test the validation logic directly
            if (!string.IsNullOrEmpty(settings.Action))
            {
                var validActions = new[] { "get", "set", "list", "reset" };
                if (!validActions.Contains(settings.Action.ToLowerInvariant()))
                {
                    return Error.ValidationFailed(
                        $"Action must be one of: {string.Join(", ", validActions)}",
                        new Dictionary<string, object> { ["ProvidedAction"] = settings.Action }
                    );
                }

                if (
                    string.Equals(settings.Action, "set", StringComparison.OrdinalIgnoreCase)
                    && (string.IsNullOrEmpty(settings.Key) || string.IsNullOrEmpty(settings.Value))
                )
                {
                    return Error.ValidationFailed(
                        "Both key and value are required for 'set' action"
                    );
                }
            }

            return Result.Success();
        }

        // Override to prevent actual execution during tests
        protected override Result<int> ExecuteCommand(CommandContext context, Settings settings)
        {
            return ExitCodes.Success;
        }
    }

    /// <summary>
    /// Helper class for creating CommandContext
    /// </summary>
    private class FakeRemainingArguments : IRemainingArguments
    {
        public IReadOnlyList<string> Raw => Array.Empty<string>();
        public ILookup<string, string?> Parsed =>
            Array.Empty<string>().ToLookup(x => x, x => (string?)null);
    }

    #endregion
}
