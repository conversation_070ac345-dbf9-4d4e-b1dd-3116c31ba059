<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>NU1701;NU1604;NU1602</WarningsNotAsErrors>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="FluentAssertions" Version="6.12.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Spectre.Console" Version="0.50.0" />
    <PackageReference Include="Spectre.Console.Testing" Version="0.49.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="System.IO.Abstractions.TestingHelpers" Version="21.1.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\src\presentation\NuGone.Cli\NuGone.Cli.csproj" />
    <ProjectReference Include="..\..\..\src\core\NuGone.Application\NuGone.Application.csproj" />
    <ProjectReference Include="..\..\..\src\core\NuGone.Domain\NuGone.Domain.csproj" />
  </ItemGroup>
</Project>
