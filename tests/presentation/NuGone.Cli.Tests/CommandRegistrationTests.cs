using FluentAssertions;
using NuGone.Cli.Features.AnalyzeCommand.Commands;
using NuGone.Cli.Features.ConfigCommand.Commands;
using NuGone.Cli.Features.RemoveCommand.Commands;
using Spectre.Console.Cli;
using Spectre.Console.Testing;

namespace NuGone.Cli.Tests;

/// <summary>
/// Tests for CLI command registration and discovery mechanisms.
/// Validates RFC-0001: CLI Architecture And Command Design - Command Registration.
/// </summary>
public class CommandRegistrationTests
{
    [Fact]
    public void CommandApp_ShouldHaveCorrectApplicationMetadata()
    {
        // Arrange & Act
        var app = CreateCommandApp();
        
        // Assert
        // Note: Spectre.Console.Cli doesn't expose application name/version directly
        // We verify this through the configuration process
        app.Should().NotBeNull();
    }

    [Fact]
    public void CommandApp_ShouldRegisterAnalyzeCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "analyze", "--help" }, console);

        // Assert
        result.Should().NotBeNull();
        var output = console.Output;
        output.Should().Contain("analyze");
        output.Should().Contain("Analyze project(s) for unused NuGet packages");
    }

    [Fact]
    public void CommandApp_ShouldRegisterRemoveCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "remove", "--help" }, console);

        // Assert
        result.Should().NotBeNull();
        var output = console.Output;
        output.Should().Contain("remove");
        output.Should().Contain("Remove unused NuGet packages from project(s)");
    }

    [Fact]
    public void CommandApp_ShouldRegisterConfigCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "config", "--help" }, console);

        // Assert
        result.Should().NotBeNull();
        var output = console.Output;
        output.Should().Contain("config");
        output.Should().Contain("Manage NuGone configuration settings");
    }

    [Fact]
    public void CommandApp_ShouldShowHelpWhenNoCommandProvided()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "--help" }, console);

        // Assert
        result.Should().NotBeNull();
        var output = console.Output;
        output.Should().Contain("nugone");
        output.Should().Contain("analyze");
        output.Should().Contain("remove");
        output.Should().Contain("config");
    }

    [Fact]
    public void CommandApp_ShouldIncludeExamplesForAnalyzeCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "analyze", "--help" }, console);

        // Assert
        var output = console.Output;
        output.Should().Contain("EXAMPLES:");
        output.Should().Contain("MySolution.sln");
        output.Should().Contain("--format json");
    }

    [Fact]
    public void CommandApp_ShouldIncludeExamplesForRemoveCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "remove", "--help" }, console);

        // Assert
        var output = console.Output;
        output.Should().Contain("EXAMPLES:");
        output.Should().Contain("MySolution.sln");
        output.Should().Contain("--exclude");
    }

    [Fact]
    public void CommandApp_ShouldIncludeExamplesForConfigCommand()
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { "config", "--help" }, console);

        // Assert
        var output = console.Output;
        output.Should().Contain("EXAMPLES:");
        output.Should().Contain("config list");
        output.Should().Contain("config set");
    }

    [Theory]
    [InlineData("invalid-command")]
    [InlineData("unknown")]
    [InlineData("test")]
    public void CommandApp_ShouldHandleUnknownCommands(string unknownCommand)
    {
        // Arrange
        var app = CreateCommandApp();
        var console = new TestConsole();

        // Act
        var result = app.RunAsync(new[] { unknownCommand }, console);

        // Assert
        result.Should().NotBeNull();
        // The app should handle unknown commands gracefully
        // Exact behavior depends on Spectre.Console.Cli implementation
    }

    /// <summary>
    /// Creates a CommandApp instance with the same configuration as Program.cs
    /// This ensures we're testing the actual command registration logic.
    /// </summary>
    private static CommandApp CreateCommandApp()
    {
        var app = new CommandApp();

        app.Configure(config =>
        {
            // Configure application metadata (same as Program.cs)
            config.SetApplicationName("nugone");
            config.SetApplicationVersion("0.1.0");

            // Add commands following RFC-0001 structure (same as Program.cs)
            config
                .AddCommand<AnalyzeCommand>("analyze")
                .WithDescription("Analyze project(s) for unused NuGet packages")
                .WithExample(new[] { "analyze", "--project", "MySolution.sln" })
                .WithExample(new[] { "analyze", "--dry-run", "--format", "json" });

            config
                .AddCommand<RemoveCommand>("remove")
                .WithDescription("Remove unused NuGet packages from project(s)")
                .WithExample(new[] { "remove", "--project", "MySolution.sln" })
                .WithExample(new[] { "remove", "--exclude", "System.Text.Json" });

            config
                .AddCommand<ConfigCommand>("config")
                .WithDescription("Manage NuGone configuration settings")
                .WithExample(new[] { "config", "list" })
                .WithExample(new[] { "config", "set", "excludeNamespaces", "System.Text.Json" });
        });

        return app;
    }
}
