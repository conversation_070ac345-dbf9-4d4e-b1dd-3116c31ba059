using FluentAssertions;
using NuGone.Cli.Shared.Constants;
using NuGone.Cli.Shared.Models;
using NuGone.Cli.Shared.Utilities;
using Spectre.Console.Cli;
using Spectre.Console.Testing;

namespace NuGone.Cli.Tests;

/// <summary>
/// Tests for BaseCommand functionality and error handling patterns.
/// Validates RFC-0001: CLI Architecture And Command Design - Error Handling.
/// </summary>
public class BaseCommandTests
{
    [Fact]
    public void BaseCommand_ShouldImplementResultPattern()
    {
        // Arrange
        var command = new TestSyncCommand();
        var context = new CommandContext(new FakeRemainingArguments(), "test", null);
        var settings = new TestSettings();

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.Success);
    }

    [Fact]
    public void BaseCommand_ShouldHandleAsyncCommands()
    {
        // Arrange
        var command = new TestAsyncCommand();
        var context = new CommandContext(new FakeRemainingArguments(), "test", null);
        var settings = new TestSettings();

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.Success);
    }

    [Fact]
    public void BaseCommand_ShouldHandleErrorsInSyncCommands()
    {
        // Arrange
        var command = new TestSyncCommandWithError();
        var context = new CommandContext(new FakeRemainingArguments(), "test", null);
        var settings = new TestSettings();

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.InvalidArgument);
    }

    [Fact]
    public void BaseCommand_ShouldHandleErrorsInAsyncCommands()
    {
        // Arrange
        var command = new TestAsyncCommandWithError();
        var context = new CommandContext(new FakeRemainingArguments(), "test", null);
        var settings = new TestSettings();

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.OperationFailed);
    }

    [Fact]
    public void BaseCommand_ShouldHandleUnexpectedExceptions()
    {
        // Arrange
        var command = new TestCommandWithException();
        var context = new CommandContext(new FakeRemainingArguments(), "test", null);
        var settings = new TestSettings();

        // Act
        var result = command.Execute(context, settings);

        // Assert
        result.Should().Be(ExitCodes.UnexpectedError);
    }

    [Fact]
    public void ValidateAndResolveProjectPath_ShouldReturnCurrentDirectoryWhenPathIsEmpty()
    {
        // Arrange
        var command = new TestSyncCommand();

        // Act
        var result = command.TestValidateAndResolveProjectPath(string.Empty);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(Path.GetFullPath(Directory.GetCurrentDirectory()));
    }

    [Fact]
    public void ValidateAndResolveProjectPath_ShouldReturnErrorForNonExistentPath()
    {
        // Arrange
        var command = new TestSyncCommand();
        var nonExistentPath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());

        // Act
        var result = command.TestValidateAndResolveProjectPath(nonExistentPath);

        // Assert
        result.IsFailure.Should().BeTrue();
        result.Error.Code.Should().Be("INVALID_ARGUMENT");
        result.Error.Message.Should().Contain("Project path does not exist");
    }

    [Fact]
    public void ValidateAndResolveProjectPath_ShouldReturnFullPathForValidPath()
    {
        // Arrange
        var command = new TestSyncCommand();
        var validPath = Directory.GetCurrentDirectory();

        // Act
        var result = command.TestValidateAndResolveProjectPath(validPath);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(Path.GetFullPath(validPath));
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    [InlineData("   ")]
    public void ValidateAndResolveProjectPath_ShouldHandleEmptyOrNullPaths(string? path)
    {
        // Arrange
        var command = new TestSyncCommand();

        // Act
        var result = command.TestValidateAndResolveProjectPath(path!);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Value.Should().Be(Path.GetFullPath(Directory.GetCurrentDirectory()));
    }

    [Fact]
    public void ValidateSettings_ShouldReturnSuccessByDefault()
    {
        // Arrange
        var command = new TestSyncCommand();
        var settings = new TestSettings();

        // Act
        var result = command.TestValidateSettings(settings);

        // Assert
        result.IsSuccess.Should().BeTrue();
    }

    // Test command implementations for testing BaseCommand functionality
    private class TestSettings : CommandSettings
    {
        public bool Verbose { get; set; }
    }

    private class TestSyncCommand : BaseCommand<TestSettings>
    {
        protected override Result<int> ExecuteCommand(CommandContext context, TestSettings settings)
        {
            return Result<int>.Success(ExitCodes.Success);
        }

        public Result<string> TestValidateAndResolveProjectPath(string projectPath)
        {
            return ValidateAndResolveProjectPath(projectPath);
        }

        public Result TestValidateSettings(TestSettings settings)
        {
            return ValidateSettings(settings);
        }

        protected override bool IsVerboseMode(TestSettings? settings)
        {
            return settings?.Verbose ?? false;
        }
    }

    private class TestAsyncCommand : BaseCommand<TestSettings>, IAsyncCommand<TestSettings>
    {
        protected override async Task<Result<int>> ExecuteCommandAsync(CommandContext context, TestSettings settings)
        {
            await Task.Delay(1); // Simulate async work
            return Result<int>.Success(ExitCodes.Success);
        }

        protected override bool IsVerboseMode(TestSettings? settings)
        {
            return settings?.Verbose ?? false;
        }
    }

    private class TestSyncCommandWithError : BaseCommand<TestSettings>
    {
        protected override Result<int> ExecuteCommand(CommandContext context, TestSettings settings)
        {
            return Error.InvalidArgument("Test error message", "testParameter");
        }

        protected override bool IsVerboseMode(TestSettings? settings)
        {
            return settings?.Verbose ?? false;
        }
    }

    private class TestAsyncCommandWithError : BaseCommand<TestSettings>, IAsyncCommand<TestSettings>
    {
        protected override async Task<Result<int>> ExecuteCommandAsync(CommandContext context, TestSettings settings)
        {
            await Task.Delay(1); // Simulate async work
            return Error.OperationFailed("test-operation", "Test failure reason");
        }

        protected override bool IsVerboseMode(TestSettings? settings)
        {
            return settings?.Verbose ?? false;
        }
    }

    private class TestCommandWithException : BaseCommand<TestSettings>
    {
        protected override Result<int> ExecuteCommand(CommandContext context, TestSettings settings)
        {
            throw new InvalidOperationException("Test exception");
        }

        protected override bool IsVerboseMode(TestSettings? settings)
        {
            return settings?.Verbose ?? false;
        }
    }

    // Helper class for creating CommandContext
    private class FakeRemainingArguments : IRemainingArguments
    {
        public string? Raw => null;
        public string[] Parsed => Array.Empty<string>();
    }
}
