<Project>
  <!--
    Central Package Management for NuGone Solution

    This file centralizes package version management across all projects in the solution.
    Benefits:
    - Ensures consistent package versions across all projects
    - Simplifies package updates (single location)
    - Reduces package version conflicts
    - Improves maintainability

    Usage:
    - In project files, use <PackageReference Include="PackageName" /> without Version attribute
    - Package versions are defined here and automatically applied to all projects
    - To override a version for a specific project, use Version attribute in that project
  -->
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <!-- Core Framework Packages -->
  <ItemGroup Label="Microsoft Extensions">
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
  </ItemGroup>
  <!-- Testing Framework Packages -->
  <ItemGroup Label="Testing">
    <PackageVersion Include="xunit" Version="2.9.2" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="FluentAssertions" Version="6.12.1" />
  </ItemGroup>
  <!-- Application Framework Packages -->
  <ItemGroup Label="Application">
    <PackageVersion Include="MediatR" Version="12.4.1" />
    <PackageVersion Include="System.Text.Json" Version="9.0.0" />
    <PackageVersion Include="System.IO.Abstractions" Version="21.1.3" />
    <PackageVersion Include="System.IO.Abstractions.TestingHelpers" Version="21.1.3" />
  </ItemGroup>
  <!-- UI/Console Packages -->
  <ItemGroup Label="Console">
    <PackageVersion Include="Spectre.Console" Version="0.50.0" />
    <PackageVersion Include="Spectre.Console.Cli" Version="0.50.0" />
    <PackageVersion Include="Spectre.Console.Testing" Version="0.49.1" />
  </ItemGroup>
  <!-- Code Analysis Packages -->
  <ItemGroup Label="Code Analysis">
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" />
  </ItemGroup>
</Project>
